import { useEffect } from 'preact/hooks'
import { ComponentChildren } from 'preact'
import { useAppDispatch } from '@/hooks/redux'
import { loadUserFromStorage } from '@/store/authSlice'

interface AuthProviderProps {
  children: ComponentChildren
}

export function AuthProvider({ children }: AuthProviderProps) {
  const dispatch = useAppDispatch()

  useEffect(() => {
    // Load user from localStorage on app start
    dispatch(loadUserFromStorage())
  }, [dispatch])

  return <>{children}</>
}