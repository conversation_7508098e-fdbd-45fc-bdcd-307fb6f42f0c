# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Project Overview

This is a Preact + Vite + TypeScript project with a minimal setup. The application is a basic counter app using Preact hooks.

## Development Commands

- `npm run dev` - Start development server with hot module replacement
- `npm run build` - Build for production (runs TypeScript compilation followed by Vite build)
- `npm run preview` - Preview production build locally

## Architecture

- **Entry point**: `src/main.tsx` renders the App component into the DOM
- **Main component**: `src/app.tsx` contains the core application logic with a simple counter state
- **Build tool**: Vite with Preact preset for fast development and building
- **TypeScript**: Configured with separate configs for app and node environments

## Key Files

- `src/app.tsx` - Main application component with counter functionality
- `src/main.tsx` - Application entry point and DOM rendering
- `vite.config.ts` - Vite configuration with Preact preset
- `package.json` - Dependencies: Preact for UI, Vite for tooling, TypeScript for type checking

## Development Notes

- Uses Preact hooks (`useState`) for state management
- CSS files are imported directly into components
- No testing framework is currently configured
- No linting tools are set up in the current configuration