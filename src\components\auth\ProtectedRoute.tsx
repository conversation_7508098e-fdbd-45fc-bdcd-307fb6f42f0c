import { ComponentChildren } from 'preact'
import { useAppSelector } from '@/hooks/redux'

interface ProtectedRouteProps {
  children: ComponentChildren
}

export function ProtectedRoute({ children }: ProtectedRouteProps) {
  const { isAuthenticated } = useAppSelector((state) => state.auth)
  
  // If not authenticated, children won't render (handled by main routing logic)
  return isAuthenticated ? <>{children}</> : null
}