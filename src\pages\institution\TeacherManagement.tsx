import { useState } from 'preact/hooks'
import { <PERSON>, Card<PERSON><PERSON>nt, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'

interface Teacher {
  id: string
  name: string
  email: string
  phone: string
  specialties: string[]
  experience: number
  education: string
  status: 'active' | 'inactive' | 'pending'
  joinDate: string
  avatar?: string
  rating: number
  totalStudents: number
  totalCourses: number
  hourlyRate: number
  bio?: string
  certifications: string[]
  languages: string[]
}

interface TeacherFormData extends Omit<Teacher, 'id' | 'totalStudents' | 'totalCourses' | 'rating'> {}

function TeacherForm({ 
  teacher, 
  onSave, 
  onCancel 
}: { 
  teacher?: Teacher | null
  onSave: (teacher: TeacherFormData) => void
  onCancel: () => void 
}) {
  const [formData, setFormData] = useState<TeacherFormData>({
    name: teacher?.name || '',
    email: teacher?.email || '',
    phone: teacher?.phone || '',
    specialties: teacher?.specialties || [],
    experience: teacher?.experience || 0,
    education: teacher?.education || '',
    status: teacher?.status || 'active',
    joinDate: teacher?.joinDate || new Date().toISOString().split('T')[0],
    hourlyRate: teacher?.hourlyRate || 0,
    bio: teacher?.bio || '',
    certifications: teacher?.certifications || [],
    languages: teacher?.languages || []
  })

  const handleSubmit = (e: Event) => {
    e.preventDefault()
    if (formData.name && formData.email && formData.phone) {
      onSave(formData)
    }
  }

  const handleSpecialtyChange = (specialty: string, checked: boolean) => {
    if (checked) {
      setFormData({
        ...formData,
        specialties: [...formData.specialties, specialty]
      })
    } else {
      setFormData({
        ...formData,
        specialties: formData.specialties.filter(s => s !== specialty)
      })
    }
  }

  const availableSpecialties = [
    '数学', '英语', '语文', '物理', '化学', '生物',
    '历史', '地理', '政治', '编程', '美术', '音乐'
  ]

  const educationLevels = [
    { value: 'bachelor', label: '本科' },
    { value: 'master', label: '硕士' },
    { value: 'phd', label: '博士' },
    { value: 'other', label: '其他' }
  ]

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <div className="bg-white rounded-lg max-w-4xl w-full max-h-[90vh] overflow-y-auto">
        <div className="p-6">
          <h2 className="text-xl font-bold text-gray-900 mb-6">
            {teacher ? '编辑教师' : '添加教师'}
          </h2>
          
          <form onSubmit={handleSubmit} className="space-y-6">
            {/* 基本信息 */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">姓名 *</label>
                <input
                  type="text"
                  value={formData.name}
                  onChange={(e) => setFormData({
                    ...formData,
                    name: (e.target as HTMLInputElement).value
                  })}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  required
                />
              </div>
              
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">邮箱 *</label>
                <input
                  type="email"
                  value={formData.email}
                  onChange={(e) => setFormData({
                    ...formData,
                    email: (e.target as HTMLInputElement).value
                  })}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  required
                />
              </div>
              
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">手机号 *</label>
                <input
                  type="tel"
                  value={formData.phone}
                  onChange={(e) => setFormData({
                    ...formData,
                    phone: (e.target as HTMLInputElement).value
                  })}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  required
                />
              </div>
              
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">教学经验（年）</label>
                <input
                  type="number"
                  value={formData.experience}
                  onChange={(e) => setFormData({
                    ...formData,
                    experience: parseInt((e.target as HTMLInputElement).value) || 0
                  })}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                />
              </div>
              
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">学历</label>
                <select
                  value={formData.education}
                  onChange={(e) => setFormData({
                    ...formData,
                    education: (e.target as HTMLSelectElement).value
                  })}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                >
                  <option value="">请选择学历</option>
                  {educationLevels.map(level => (
                    <option key={level.value} value={level.value}>{level.label}</option>
                  ))}
                </select>
              </div>
              
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">状态</label>
                <select
                  value={formData.status}
                  onChange={(e) => setFormData({
                    ...formData,
                    status: (e.target as HTMLSelectElement).value as Teacher['status']
                  })}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                >
                  <option value="active">在职</option>
                  <option value="inactive">离职</option>
                  <option value="pending">待入职</option>
                </select>
              </div>
              
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">入职日期</label>
                <input
                  type="date"
                  value={formData.joinDate}
                  onChange={(e) => setFormData({
                    ...formData,
                    joinDate: (e.target as HTMLInputElement).value
                  })}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                />
              </div>
              
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">课时费（元/小时）</label>
                <input
                  type="number"
                  value={formData.hourlyRate}
                  onChange={(e) => setFormData({
                    ...formData,
                    hourlyRate: parseInt((e.target as HTMLInputElement).value) || 0
                  })}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                />
              </div>
            </div>

            {/* 专业领域 */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">专业领域</label>
              <div className="grid grid-cols-3 md:grid-cols-4 gap-2">
                {availableSpecialties.map((specialty) => (
                  <label key={specialty} className="flex items-center space-x-2">
                    <input
                      type="checkbox"
                      checked={formData.specialties.includes(specialty)}
                      onChange={(e) => handleSpecialtyChange(specialty, (e.target as HTMLInputElement).checked)}
                      className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                    />
                    <span className="text-sm text-gray-700">{specialty}</span>
                  </label>
                ))}
              </div>
            </div>

            {/* 个人简介 */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">个人简介</label>
              <textarea
                value={formData.bio}
                onChange={(e) => setFormData({
                  ...formData,
                  bio: (e.target as HTMLTextAreaElement).value
                })}
                rows={4}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                placeholder="请输入教师的个人简介和教学特色..."
              />
            </div>

            {/* 按钮 */}
            <div className="flex justify-end space-x-4 pt-4 border-t">
              <Button
                type="button"
                variant="outline"
                onClick={onCancel}
              >
                取消
              </Button>
              <Button type="submit">
                {teacher ? '更新' : '添加'}
              </Button>
            </div>
          </form>
        </div>
      </div>
    </div>
  )
}

function TeacherList({ teachers, onEdit, onDelete }: {
  teachers: Teacher[]
  onEdit: (teacher: Teacher) => void
  onDelete: (id: string) => void
}) {
  const getStatusColor = (status: Teacher['status']) => {
    const colors = {
      active: 'bg-green-100 text-green-800',
      inactive: 'bg-red-100 text-red-800',
      pending: 'bg-yellow-100 text-yellow-800'
    }
    return colors[status]
  }

  const getStatusLabel = (status: Teacher['status']) => {
    const labels = {
      active: '在职',
      inactive: '离职',
      pending: '待入职'
    }
    return labels[status]
  }

  const getEducationLabel = (education: string) => {
    const labels = {
      bachelor: '本科',
      master: '硕士',
      phd: '博士',
      other: '其他'
    }
    return labels[education as keyof typeof labels] || education
  }

  return (
    <div className="grid gap-4">
      {teachers.map((teacher) => (
        <Card key={teacher.id} className="hover:shadow-md transition-shadow">
          <CardContent className="p-6">
            <div className="flex justify-between items-start mb-4">
              <div className="flex items-center space-x-4">
                <div className="w-16 h-16 bg-purple-100 rounded-full flex items-center justify-center">
                  <span className="text-purple-600 font-semibold text-xl">
                    {teacher.name.charAt(0)}
                  </span>
                </div>
                <div>
                  <h3 className="text-lg font-semibold text-gray-900">{teacher.name}</h3>
                  <div className="flex items-center space-x-2 mt-1">
                    <span className="text-sm text-gray-600">{teacher.experience}年经验</span>
                    <span className="text-gray-300">•</span>
                    <span className="text-sm text-gray-600">{getEducationLabel(teacher.education)}</span>
                    <span className={`px-2 py-1 rounded-full text-xs font-medium ${getStatusColor(teacher.status)}`}>
                      {getStatusLabel(teacher.status)}
                    </span>
                  </div>
                  <div className="flex items-center space-x-4 mt-2">
                    <div className="flex items-center">
                      <svg className="w-4 h-4 text-yellow-400 mr-1" fill="currentColor" viewBox="0 0 20 20">
                        <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z" />
                      </svg>
                      <span className="text-sm text-gray-600">{teacher.rating.toFixed(1)}</span>
                    </div>
                    <span className="text-sm text-gray-500">{teacher.totalStudents}名学员</span>
                    <span className="text-sm text-gray-500">{teacher.totalCourses}门课程</span>
                  </div>
                </div>
              </div>

              <div className="flex space-x-2">
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => onEdit(teacher)}
                >
                  编辑
                </Button>
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => onDelete(teacher.id)}
                  className="text-red-600 hover:text-red-700"
                >
                  删除
                </Button>
              </div>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-4">
              <div>
                <p className="text-sm text-gray-500">邮箱</p>
                <p className="text-gray-900">{teacher.email}</p>
              </div>

              <div>
                <p className="text-sm text-gray-500">手机号</p>
                <p className="text-gray-900">{teacher.phone}</p>
              </div>

              <div>
                <p className="text-sm text-gray-500">课时费</p>
                <p className="text-gray-900">¥{teacher.hourlyRate}/小时</p>
              </div>
            </div>

            {teacher.specialties.length > 0 && (
              <div className="mb-4">
                <p className="text-sm text-gray-500 mb-2">专业领域</p>
                <div className="flex flex-wrap gap-2">
                  {teacher.specialties.map((specialty, index) => (
                    <span key={index} className="px-2 py-1 bg-purple-100 text-purple-700 rounded text-sm">
                      {specialty}
                    </span>
                  ))}
                </div>
              </div>
            )}

            {teacher.bio && (
              <div>
                <p className="text-sm text-gray-500">个人简介</p>
                <p className="text-gray-700 text-sm mt-1">{teacher.bio}</p>
              </div>
            )}
          </CardContent>
        </Card>
      ))}
    </div>
  )
}

export function TeacherManagement() {
  const [teachers, setTeachers] = useState<Teacher[]>([
    {
      id: '1',
      name: '王老师',
      email: '<EMAIL>',
      phone: '13800138001',
      specialties: ['数学', '物理'],
      experience: 8,
      education: 'master',
      status: 'active',
      joinDate: '2022-09-01',
      rating: 4.8,
      totalStudents: 45,
      totalCourses: 6,
      hourlyRate: 200,
      bio: '拥有8年数学教学经验，擅长高中数学和物理教学，曾获得优秀教师奖。',
      certifications: ['高级教师资格证', '数学竞赛指导教师'],
      languages: ['中文', '英语']
    },
    {
      id: '2',
      name: '李老师',
      email: '<EMAIL>',
      phone: '13800138002',
      specialties: ['英语', '语文'],
      experience: 5,
      education: 'bachelor',
      status: 'active',
      joinDate: '2023-02-15',
      rating: 4.6,
      totalStudents: 32,
      totalCourses: 4,
      hourlyRate: 180,
      bio: '英语专业毕业，具有丰富的英语教学经验，注重培养学生的口语表达能力。',
      certifications: ['英语专业八级', '教师资格证'],
      languages: ['中文', '英语']
    },
    {
      id: '3',
      name: '张老师',
      email: '<EMAIL>',
      phone: '13800138003',
      specialties: ['编程', '数学'],
      experience: 3,
      education: 'master',
      status: 'pending',
      joinDate: '2024-01-10',
      rating: 4.9,
      totalStudents: 28,
      totalCourses: 3,
      hourlyRate: 250,
      bio: '计算机科学硕士，专注于青少年编程教育，擅长Python和JavaScript教学。',
      certifications: ['软件工程师认证', '青少年编程指导师'],
      languages: ['中文', '英语']
    }
  ])

  const [showForm, setShowForm] = useState(false)
  const [editingTeacher, setEditingTeacher] = useState<Teacher | null>(null)
  const [searchTerm, setSearchTerm] = useState('')
  const [filterSpecialty, setFilterSpecialty] = useState('all')
  const [filterStatus, setFilterStatus] = useState('all')

  const handleAddTeacher = () => {
    setEditingTeacher(null)
    setShowForm(true)
  }

  const handleEditTeacher = (teacher: Teacher) => {
    setEditingTeacher(teacher)
    setShowForm(true)
  }

  const handleSaveTeacher = (teacherData: TeacherFormData) => {
    if (editingTeacher) {
      setTeachers(teachers.map(t =>
        t.id === editingTeacher.id
          ? {
              ...teacherData,
              id: editingTeacher.id,
              rating: editingTeacher.rating,
              totalStudents: editingTeacher.totalStudents,
              totalCourses: editingTeacher.totalCourses
            }
          : t
      ))
    } else {
      const newTeacher: Teacher = {
        ...teacherData,
        id: (teachers.length + 1).toString(),
        rating: 0,
        totalStudents: 0,
        totalCourses: 0
      }
      setTeachers([...teachers, newTeacher])
    }
    setShowForm(false)
    setEditingTeacher(null)
  }

  const handleDeleteTeacher = (id: string) => {
    if (confirm('确定要删除这个教师吗？')) {
      setTeachers(teachers.filter(t => t.id !== id))
    }
  }

  const allSpecialties = Array.from(new Set(teachers.flatMap(t => t.specialties)))

  const filteredTeachers = teachers.filter(teacher => {
    const matchesSearch = teacher.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         teacher.email.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         teacher.specialties.some(s => s.toLowerCase().includes(searchTerm.toLowerCase()))

    const matchesSpecialty = filterSpecialty === 'all' || teacher.specialties.includes(filterSpecialty)
    const matchesStatus = filterStatus === 'all' || teacher.status === filterStatus

    return matchesSearch && matchesSpecialty && matchesStatus
  })

  return (
    <div className="p-6">
      <div className="max-w-7xl mx-auto">
        {/* 页面标题和操作 */}
        <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center mb-6">
          <div>
            <h1 className="text-2xl font-bold text-gray-900">教师管理</h1>
            <p className="text-gray-600 mt-1">管理机构的所有教师信息</p>
          </div>
          <Button onClick={handleAddTeacher}>
            + 添加教师
          </Button>
        </div>

        {/* 搜索和筛选 */}
        <Card className="mb-6">
          <CardContent className="p-4">
            <div className="flex flex-col sm:flex-row gap-4">
              <div className="flex-1">
                <input
                  type="text"
                  placeholder="搜索教师姓名、邮箱或专业领域..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm((e.target as HTMLInputElement).value)}
                  className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                />
              </div>
              <div className="sm:w-48">
                <select
                  value={filterSpecialty}
                  onChange={(e) => setFilterSpecialty((e.target as HTMLSelectElement).value)}
                  className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                >
                  <option value="all">所有专业</option>
                  {allSpecialties.map(specialty => (
                    <option key={specialty} value={specialty}>{specialty}</option>
                  ))}
                </select>
              </div>
              <div className="sm:w-32">
                <select
                  value={filterStatus}
                  onChange={(e) => setFilterStatus((e.target as HTMLSelectElement).value)}
                  className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                >
                  <option value="all">所有状态</option>
                  <option value="active">在职</option>
                  <option value="inactive">离职</option>
                  <option value="pending">待入职</option>
                </select>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* 教师列表 */}
        {filteredTeachers.length > 0 ? (
          <TeacherList
            teachers={filteredTeachers}
            onEdit={handleEditTeacher}
            onDelete={handleDeleteTeacher}
          />
        ) : (
          <Card>
            <CardContent className="p-12 text-center">
              <div className="w-16 h-16 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-4">
                <svg className="w-8 h-8 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 14l9-5-9-5-9 5 9 5z" />
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 14l6.16-3.422a12.083 12.083 0 01.665 6.479A11.952 11.952 0 0012 20.055a11.952 11.952 0 00-6.824-2.998 12.078 12.078 0 01.665-6.479L12 14z" />
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 14V8" />
                </svg>
              </div>
              <h3 className="text-lg font-medium text-gray-900 mb-2">暂无教师信息</h3>
              <p className="text-gray-500 mb-4">您还没有添加任何教师，点击上方按钮添加第一个教师</p>
              <Button onClick={handleAddTeacher}>
                添加教师
              </Button>
            </CardContent>
          </Card>
        )}

        {/* 教师表单弹窗 */}
        {showForm && (
          <TeacherForm
            teacher={editingTeacher}
            onSave={handleSaveTeacher}
            onCancel={() => {
              setShowForm(false)
              setEditingTeacher(null)
            }}
          />
        )}
      </div>
    </div>
  )
}
