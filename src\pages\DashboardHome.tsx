import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { useAppSelector } from '@/hooks/redux'

export function DashboardHome() {
  const { user } = useAppSelector((state) => state.auth)

  return (
    <div className="p-6">
      <div className="max-w-7xl mx-auto">
        {/* Welcome Section */}
        <div className="mb-8">
          <h2 className="text-3xl font-bold text-gray-900 mb-2">
            欢迎回来, {user?.username}! 🎉
          </h2>
          <p className="text-gray-600">
            这是您的企业管理仪表板，您可以在这里管理您的业务。
          </p>
        </div>

        {/* Stats Cards */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
          <Card>
            <CardHeader className="pb-3">
              <CardTitle className="text-sm font-medium text-gray-500">总用户数</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">2,543</div>
              <p className="text-xs text-green-600">+12% 相比上月</p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="pb-3">
              <CardTitle className="text-sm font-medium text-gray-500">月活跃用户</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">1,892</div>
              <p className="text-xs text-green-600">+8% 相比上月</p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="pb-3">
              <CardTitle className="text-sm font-medium text-gray-500">本月收入</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">¥128,543</div>
              <p className="text-xs text-green-600">+15% 相比上月</p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="pb-3">
              <CardTitle className="text-sm font-medium text-gray-500">转换率</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">3.24%</div>
              <p className="text-xs text-red-600">-2% 相比上月</p>
            </CardContent>
          </Card>
        </div>

        {/* Main Dashboard Content */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          <Card>
            <CardHeader>
              <CardTitle>最近活动</CardTitle>
              <CardDescription>您系统中的最新活动记录</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <div className="flex items-center space-x-3">
                  <div className="w-2 h-2 bg-green-500 rounded-full"></div>
                  <div className="flex-1">
                    <p className="text-sm font-medium">新用户注册</p>
                    <p className="text-xs text-gray-500">张三 刚刚注册了账户</p>
                  </div>
                  <span className="text-xs text-gray-400">2分钟前</span>
                </div>
                
                <div className="flex items-center space-x-3">
                  <div className="w-2 h-2 bg-blue-500 rounded-full"></div>
                  <div className="flex-1">
                    <p className="text-sm font-medium">订单完成</p>
                    <p className="text-xs text-gray-500">订单 #12345 已完成</p>
                  </div>
                  <span className="text-xs text-gray-400">5分钟前</span>
                </div>
                
                <div className="flex items-center space-x-3">
                  <div className="w-2 h-2 bg-yellow-500 rounded-full"></div>
                  <div className="flex-1">
                    <p className="text-sm font-medium">系统维护</p>
                    <p className="text-xs text-gray-500">定期维护已完成</p>
                  </div>
                  <span className="text-xs text-gray-400">1小时前</span>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle>快速操作</CardTitle>
              <CardDescription>常用功能的快速入口</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-2 gap-4">
                <button className="h-20 flex flex-col items-center justify-center border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors">
                  <svg className="h-6 w-6 mb-2 text-gray-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197m13.5-9a2.5 2.5 0 11-5 0 2.5 2.5 0 015 0z"></path>
                  </svg>
                  <span className="text-sm font-medium">用户管理</span>
                </button>
                
                <button className="h-20 flex flex-col items-center justify-center border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors">
                  <svg className="h-6 w-6 mb-2 text-gray-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"></path>
                  </svg>
                  <span className="text-sm font-medium">数据报告</span>
                </button>
                
                <button className="h-20 flex flex-col items-center justify-center border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors">
                  <svg className="h-6 w-6 mb-2 text-gray-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z"></path>
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
                  </svg>
                  <span className="text-sm font-medium">系统设置</span>
                </button>
                
                <button className="h-20 flex flex-col items-center justify-center border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors">
                  <svg className="h-6 w-6 mb-2 text-gray-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8.228 9c.549-1.165 2.03-2 3.772-2 2.21 0 4 1.343 4 3 0 1.4-1.278 2.575-3.006 2.907-.542.104-.994.54-.994 1.093m0 3h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                  </svg>
                  <span className="text-sm font-medium">帮助支持</span>
                </button>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Additional content to test scrolling */}
        <div className="mt-8 space-y-6">
          <Card>
            <CardHeader>
              <CardTitle>教学进度统计</CardTitle>
              <CardDescription>各科目教学进度和完成情况</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <div className="flex justify-between items-center">
                  <span className="text-sm font-medium">语文课程</span>
                  <div className="flex items-center space-x-2">
                    <div className="w-24 bg-gray-200 rounded-full h-2">
                      <div className="bg-blue-600 h-2 rounded-full" style="width: 75%"></div>
                    </div>
                    <span className="text-sm text-gray-500">75%</span>
                  </div>
                </div>
                <div className="flex justify-between items-center">
                  <span className="text-sm font-medium">数学课程</span>
                  <div className="flex items-center space-x-2">
                    <div className="w-24 bg-gray-200 rounded-full h-2">
                      <div className="bg-green-600 h-2 rounded-full" style="width: 82%"></div>
                    </div>
                    <span className="text-sm text-gray-500">82%</span>
                  </div>
                </div>
                <div className="flex justify-between items-center">
                  <span className="text-sm font-medium">英语课程</span>
                  <div className="flex items-center space-x-2">
                    <div className="w-24 bg-gray-200 rounded-full h-2">
                      <div className="bg-yellow-600 h-2 rounded-full" style="width: 68%"></div>
                    </div>
                    <span className="text-sm text-gray-500">68%</span>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle>学员考勤概况</CardTitle>
              <CardDescription>本月学员出勤情况统计</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                <div className="text-center">
                  <div className="text-2xl font-bold text-green-600">96%</div>
                  <div className="text-sm text-gray-500">总出勤率</div>
                </div>
                <div className="text-center">
                  <div className="text-2xl font-bold text-blue-600">486</div>
                  <div className="text-sm text-gray-500">出勤人次</div>
                </div>
                <div className="text-center">
                  <div className="text-2xl font-bold text-yellow-600">12</div>
                  <div className="text-sm text-gray-500">请假人次</div>
                </div>
                <div className="text-center">
                  <div className="text-2xl font-bold text-red-600">8</div>
                  <div className="text-sm text-gray-500">缺勤人次</div>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle>课程安排</CardTitle>
              <CardDescription>本周课程计划安排</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-3">
                {[
                  { time: '09:00-10:30', course: '初级语文', teacher: '张老师', room: 'A101' },
                  { time: '10:45-12:15', course: '中级数学', teacher: '李老师', room: 'B203' },
                  { time: '14:00-15:30', course: '英语口语', teacher: '王老师', room: 'C305' },
                  { time: '15:45-17:15', course: '写作训练', teacher: '赵老师', room: 'A102' },
                  { time: '19:00-20:30', course: '高级数学', teacher: '陈老师', room: 'B205' },
                ].map((item, index) => (
                  <div key={index} className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                    <div className="flex items-center space-x-4">
                      <div className="text-sm font-medium text-blue-600">{item.time}</div>
                      <div className="text-sm font-medium">{item.course}</div>
                    </div>
                    <div className="flex items-center space-x-4 text-sm text-gray-500">
                      <span>{item.teacher}</span>
                      <span>{item.room}</span>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle>待办事项</CardTitle>
              <CardDescription>需要处理的重要事务</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-3">
                {[
                  { task: '审核新学员报名申请', priority: '高', status: '待处理' },
                  { task: '准备期中考试试卷', priority: '中', status: '进行中' },
                  { task: '更新课程表安排', priority: '低', status: '待开始' },
                  { task: '联系家长会议安排', priority: '高', status: '待处理' },
                  { task: '检查教室设备状态', priority: '中', status: '已完成' },
                ].map((item, index) => (
                  <div key={index} className="flex items-center justify-between p-3 border border-gray-200 rounded-lg">
                    <div className="flex items-center space-x-3">
                      <div className={`w-2 h-2 rounded-full ${
                        item.priority === '高' ? 'bg-red-500' : 
                        item.priority === '中' ? 'bg-yellow-500' : 'bg-green-500'
                      }`}></div>
                      <span className="text-sm font-medium">{item.task}</span>
                    </div>
                    <div className="flex items-center space-x-3">
                      <span className={`text-xs px-2 py-1 rounded-full ${
                        item.status === '已完成' ? 'bg-green-100 text-green-800' :
                        item.status === '进行中' ? 'bg-blue-100 text-blue-800' :
                        'bg-gray-100 text-gray-800'
                      }`}>
                        {item.status}
                      </span>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  )
}