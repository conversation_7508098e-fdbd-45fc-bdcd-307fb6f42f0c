{"name": "pc", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "tsc -b && vite build", "preview": "vite preview"}, "dependencies": {"@preact/compat": "^18.3.1", "@reduxjs/toolkit": "^2.8.2", "@rollup/rollup-win32-x64-msvc": "^4.44.0", "@shadcn/ui": "^0.0.4", "@types/node": "^24.0.4", "clsx": "^2.1.1", "preact": "^10.26.5", "preact-router": "^4.1.2", "react-redux": "^9.2.0", "tailwind-merge": "^3.3.1"}, "devDependencies": {"@preact/preset-vite": "^2.10.1", "autoprefixer": "^10.4.21", "postcss": "^8.5.6", "tailwindcss": "^3.4.0", "typescript": "~5.8.3", "vite": "^5.2.10"}}