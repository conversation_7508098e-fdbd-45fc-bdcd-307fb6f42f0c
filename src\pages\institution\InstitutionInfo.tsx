import { useState } from 'preact/hooks'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'

interface InstitutionData {
  name: string
  establishedDate: string
  registrationNumber: string
  legalRepresentative: string
  businessScope: string
  website: string
  description: string
  logo?: string
}

interface ContactInfo {
  id: string
  type: string
  value: string
  label: string
  isPrimary: boolean
}

function BasicInfoSection() {
  const [isEditing, setIsEditing] = useState(false)
  const [institutionData, setInstitutionData] = useState<InstitutionData>({
    name: '智慧教育培训机构',
    establishedDate: '2020-03-15',
    registrationNumber: '91310000MA1234567X',
    legalRepresentative: '张三',
    businessScope: '教育培训、技能培训、职业培训',
    website: 'https://www.smartedu.com',
    description: '专注于为学员提供高质量的教育培训服务，致力于培养专业技能人才。'
  })

  const handleSave = () => {
    // 这里应该调用API保存数据
    console.log('保存机构信息:', institutionData)
    setIsEditing(false)
  }

  const handleCancel = () => {
    setIsEditing(false)
    // 重置数据到原始状态
  }

  return (
    <Card>
      <CardHeader className="flex flex-row items-center justify-between">
        <CardTitle>机构基本信息</CardTitle>
        <div className="flex space-x-2">
          {isEditing ? (
            <>
              <Button
                onClick={handleSave}
                size="sm"
              >
                保存
              </Button>
              <Button
                onClick={handleCancel}
                variant="outline"
                size="sm"
              >
                取消
              </Button>
            </>
          ) : (
            <Button
              onClick={() => setIsEditing(true)}
              size="sm"
            >
              编辑
            </Button>
          )}
        </div>
      </CardHeader>
      <CardContent className="space-y-6">
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div className="space-y-2">
            <label className="text-sm font-medium text-gray-700">机构名称</label>
            {isEditing ? (
              <input
                type="text"
                value={institutionData.name}
                onChange={(e) => setInstitutionData({
                  ...institutionData,
                  name: (e.target as HTMLInputElement).value
                })}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              />
            ) : (
              <p className="text-gray-900">{institutionData.name}</p>
            )}
          </div>

          <div className="space-y-2">
            <label className="text-sm font-medium text-gray-700">成立日期</label>
            {isEditing ? (
              <input
                type="date"
                value={institutionData.establishedDate}
                onChange={(e) => setInstitutionData({
                  ...institutionData,
                  establishedDate: (e.target as HTMLInputElement).value
                })}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              />
            ) : (
              <p className="text-gray-900">{institutionData.establishedDate}</p>
            )}
          </div>

          <div className="space-y-2">
            <label className="text-sm font-medium text-gray-700">注册号</label>
            {isEditing ? (
              <input
                type="text"
                value={institutionData.registrationNumber}
                onChange={(e) => setInstitutionData({
                  ...institutionData,
                  registrationNumber: (e.target as HTMLInputElement).value
                })}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              />
            ) : (
              <p className="text-gray-900">{institutionData.registrationNumber}</p>
            )}
          </div>

          <div className="space-y-2">
            <label className="text-sm font-medium text-gray-700">法定代表人</label>
            {isEditing ? (
              <input
                type="text"
                value={institutionData.legalRepresentative}
                onChange={(e) => setInstitutionData({
                  ...institutionData,
                  legalRepresentative: (e.target as HTMLInputElement).value
                })}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              />
            ) : (
              <p className="text-gray-900">{institutionData.legalRepresentative}</p>
            )}
          </div>

          <div className="space-y-2">
            <label className="text-sm font-medium text-gray-700">经营范围</label>
            {isEditing ? (
              <input
                type="text"
                value={institutionData.businessScope}
                onChange={(e) => setInstitutionData({
                  ...institutionData,
                  businessScope: (e.target as HTMLInputElement).value
                })}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              />
            ) : (
              <p className="text-gray-900">{institutionData.businessScope}</p>
            )}
          </div>

          <div className="space-y-2">
            <label className="text-sm font-medium text-gray-700">官方网站</label>
            {isEditing ? (
              <input
                type="url"
                value={institutionData.website}
                onChange={(e) => setInstitutionData({
                  ...institutionData,
                  website: (e.target as HTMLInputElement).value
                })}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              />
            ) : (
              <a href={institutionData.website} target="_blank" rel="noopener noreferrer" className="text-blue-600 hover:underline">
                {institutionData.website}
              </a>
            )}
          </div>
        </div>

        <div className="space-y-2">
          <label className="text-sm font-medium text-gray-700">机构描述</label>
          {isEditing ? (
            <textarea
              value={institutionData.description}
              onChange={(e) => setInstitutionData({
                ...institutionData,
                description: (e.target as HTMLTextAreaElement).value
              })}
              rows={4}
              className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            />
          ) : (
            <p className="text-gray-900">{institutionData.description}</p>
          )}
        </div>
      </CardContent>
    </Card>
  )
}

function ContactInfoSection() {
  const [contacts, setContacts] = useState<ContactInfo[]>([
    { id: '1', type: 'phone', value: '************', label: '客服热线', isPrimary: true },
    { id: '2', type: 'email', value: '<EMAIL>', label: '客服邮箱', isPrimary: true },
    { id: '3', type: 'phone', value: '021-12345678', label: '座机电话', isPrimary: false },
    { id: '4', type: 'wechat', value: 'smartedu_official', label: '微信公众号', isPrimary: false },
    { id: '5', type: 'qq', value: '123456789', label: 'QQ客服', isPrimary: false }
  ])
  const [isAddingContact, setIsAddingContact] = useState(false)
  const [newContact, setNewContact] = useState<Omit<ContactInfo, 'id'>>({
    type: 'phone',
    value: '',
    label: '',
    isPrimary: false
  })

  const handleAddContact = () => {
    if (newContact.value.trim() && newContact.label.trim()) {
      const id = (contacts.length + 1).toString()
      setContacts([...contacts, { ...newContact, id }])
      setNewContact({ type: 'phone', value: '', label: '', isPrimary: false })
      setIsAddingContact(false)
    }
  }

  const handleDeleteContact = (id: string) => {
    setContacts(contacts.filter(contact => contact.id !== id))
  }

  const getContactIcon = (type: string) => {
    switch (type) {
      case 'phone':
        return (
          <svg className="w-4 h-4 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 5a2 2 0 012-2h3.28a1 1 0 01.948.684l1.498 4.493a1 1 0 01-.502 1.21l-2.257 1.13a11.042 11.042 0 005.516 5.516l1.13-2.257a1 1 0 011.21-.502l4.493 1.498a1 1 0 01.684.949V19a2 2 0 01-2 2h-1C9.716 21 3 14.284 3 6V5z" />
          </svg>
        )
      case 'email':
        return (
          <svg className="w-4 h-4 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 8l7.89 4.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z" />
          </svg>
        )
      case 'wechat':
        return (
          <svg className="w-4 h-4 text-green-500" fill="currentColor" viewBox="0 0 24 24">
            <path d="M8.691 2.188C3.891 2.188 0 5.476 0 9.53c0 2.212 1.17 4.203 3.002 5.55a.59.59 0 01.213.665l-.39 1.48c-.019.07-.048.141-.048.213 0 .163.13.295.29.295a.326.326 0 00.167-.054l1.903-1.114a.864.864 0 01.717-.098 10.16 10.16 0 002.837.403c.276 0 .543-.027.811-.05-.857-2.578.157-4.972 1.932-6.446 1.703-1.415 3.882-1.98 5.853-1.838-.576-3.583-4.196-6.348-8.596-6.348zM5.785 5.991c.642 0 1.162.529 1.162 1.18 0 .659-.52 1.188-1.162 1.188-.642 0-1.162-.529-1.162-1.188 0-.651.52-1.18 1.162-1.18zm5.813 0c.642 0 1.162.529 1.162 1.18 0 .659-.52 1.188-1.162 1.188-.642 0-1.162-.529-1.162-1.188 0-.651.52-1.18 1.162-1.18zm6.11 5.794c0-1.682-1.691-3.045-3.773-3.045-2.082 0-3.773 1.363-3.773 3.045 0 1.682 1.691 3.045 3.773 3.045.388 0 .761-.054 1.117-.14l1.324.834c.019.013.051.025.077.025a.17.17 0 00.171-.171c0-.04-.015-.081-.028-.124l-.271-.869c1.013-.722 1.583-1.754 1.583-2.6z"/>
          </svg>
        )
      case 'qq':
        return (
          <svg className="w-4 h-4 text-blue-500" fill="currentColor" viewBox="0 0 24 24">
            <path d="M12.003 2.001c-1.8 0-3.31.806-4.651 1.694C6.36 4.428 5.55 5.43 5.003 6.313c-.44.712-.703 1.335-.703 1.748 0 .162.04.31.123.435.324-.19.674-.35 1.04-.475.133-.453.298-.885.491-1.289.68-1.425 1.516-2.532 2.385-3.265.847-.716 1.652-1.061 2.664-1.061s1.817.345 2.664 1.061c.869.733 1.704 1.84 2.385 3.265.193.404.358.836.491 1.289.366.125.716.285 1.04.475.083-.125.123-.273.123-.435 0-.413-.263-1.036-.703-1.748-.547-.883-1.357-1.885-2.349-2.618C15.313 2.807 13.803 2.001 12.003 2.001z"/>
          </svg>
        )
      default:
        return (
          <svg className="w-4 h-4 text-gray-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13.828 10.172a4 4 0 00-5.656 0l-4 4a4 4 0 105.656 5.656l1.102-1.101m-.758-4.899a4 4 0 005.656 0l4-4a4 4 0 00-5.656-5.656l-1.1 1.1" />
          </svg>
        )
    }
  }

  return (
    <Card>
      <CardHeader className="flex flex-row items-center justify-between">
        <CardTitle>联系方式</CardTitle>
        <Button
          onClick={() => setIsAddingContact(true)}
          size="sm"
        >
          + 添加联系方式
        </Button>
      </CardHeader>
      <CardContent className="space-y-4">
        {/* 添加联系方式表单 */}
        {isAddingContact && (
          <div className="border border-gray-200 rounded-lg p-4 bg-gray-50 space-y-4">
            <h4 className="font-medium text-gray-900">添加新联系方式</h4>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">类型</label>
                <select
                  value={newContact.type}
                  onChange={(e) => setNewContact({
                    ...newContact,
                    type: (e.target as HTMLSelectElement).value
                  })}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                >
                  <option value="phone">电话</option>
                  <option value="email">邮箱</option>
                  <option value="wechat">微信</option>
                  <option value="qq">QQ</option>
                </select>
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">标签</label>
                <input
                  type="text"
                  value={newContact.label}
                  onChange={(e) => setNewContact({
                    ...newContact,
                    label: (e.target as HTMLInputElement).value
                  })}
                  placeholder="如：客服热线"
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                />
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">联系方式</label>
                <input
                  type="text"
                  value={newContact.value}
                  onChange={(e) => setNewContact({
                    ...newContact,
                    value: (e.target as HTMLInputElement).value
                  })}
                  placeholder="输入联系方式"
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                />
              </div>
            </div>
            <div className="flex items-center space-x-2">
              <input
                type="checkbox"
                checked={newContact.isPrimary}
                onChange={(e) => setNewContact({
                  ...newContact,
                  isPrimary: (e.target as HTMLInputElement).checked
                })}
                className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
              />
              <label className="text-sm text-gray-700">设为主要联系方式</label>
            </div>
            <div className="flex space-x-2">
              <Button
                onClick={handleAddContact}
                size="sm"
              >
                添加
              </Button>
              <Button
                onClick={() => {
                  setIsAddingContact(false)
                  setNewContact({ type: 'phone', value: '', label: '', isPrimary: false })
                }}
                variant="outline"
                size="sm"
              >
                取消
              </Button>
            </div>
          </div>
        )}

        {/* 联系方式列表 */}
        <div className="grid gap-3">
          {contacts.map((contact) => (
            <div key={contact.id} className="flex items-center justify-between p-3 border border-gray-200 rounded-lg hover:bg-gray-50">
              <div className="flex items-center space-x-3">
                {getContactIcon(contact.type)}
                <div>
                  <div className="flex items-center space-x-2">
                    <span className="font-medium text-gray-900">{contact.label}</span>
                    {contact.isPrimary && (
                      <span className="px-2 py-0.5 bg-blue-100 text-blue-800 text-xs rounded-full">主要</span>
                    )}
                  </div>
                  <p className="text-sm text-gray-600">{contact.value}</p>
                </div>
              </div>
              <div className="flex space-x-2">
                <Button variant="ghost" size="sm" className="p-2 h-8 w-8">
                  <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15.232 5.232l3.536 3.536m-2.036-5.036a2.5 2.5 0 113.536 3.536L6.5 21.036H3v-3.572L16.732 3.732z" />
                  </svg>
                </Button>
                <Button
                  onClick={() => handleDeleteContact(contact.id)}
                  variant="ghost"
                  size="sm"
                  className="p-2 h-8 w-8 text-red-600 hover:text-red-700 hover:bg-red-50"
                >
                  <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
                  </svg>
                </Button>
              </div>
            </div>
          ))}
        </div>
      </CardContent>
    </Card>
  )
}

export function InstitutionInfo() {
  return (
    <div className="p-6">
      <div className="max-w-6xl mx-auto">
        {/* 页面标题 */}
        <div className="mb-6">
          <h1 className="text-2xl font-bold text-gray-900">机构信息</h1>
          <p className="text-gray-600 mt-1">管理机构基本信息和联系方式</p>
        </div>

        {/* 内容区域 */}
        <div className="space-y-6">
          <BasicInfoSection />
          <ContactInfoSection />
        </div>
      </div>
    </div>
  )
}