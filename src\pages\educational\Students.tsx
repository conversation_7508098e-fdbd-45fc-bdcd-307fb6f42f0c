import { useState } from 'preact/hooks'
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON><PERSON><PERSON>, Card<PERSON>itle } from '@/components/ui/card'

interface TabButtonProps {
  label: string
  active: boolean
  onClick: () => void
  count?: number
}

function TabButton({ label, active, onClick, count }: TabButtonProps) {
  return (
    <button
      onClick={onClick}
      className={`px-4 py-2 rounded-lg font-medium transition-colors relative ${
        active
          ? 'bg-blue-100 text-blue-700 border border-blue-200'
          : 'text-gray-600 hover:text-gray-900 hover:bg-gray-50'
      }`}
    >
      {label}
      {count !== undefined && (
        <span className={`ml-2 px-2 py-0.5 text-xs rounded-full ${
          active ? 'bg-blue-200 text-blue-800' : 'bg-gray-200 text-gray-600'
        }`}>
          {count}
        </span>
      )}
    </button>
  )
}

interface Student {
  id: string
  name: string
  phone: string
  email: string
  enrollDate: string
  course: string
  status: string
  progress: number
  avatar?: string
}

interface Package {
  id: string
  studentName: string
  packageName: string
  totalHours: number
  usedHours: number
  remainingHours: number
  startDate: string
  endDate: string
  status: string
  price: number
}

interface AdjustmentRecord {
  id: string
  studentName: string
  productName: string
  adjustmentType: string
  oldValue: string
  newValue: string
  reason: string
  operator: string
  date: string
}

interface GraduatedStudent {
  id: string
  name: string
  phone: string
  graduationDate: string
  course: string
  finalGrade: string
  certificateNumber: string
  currentJob?: string
}

function CurrentStudents() {
  const [searchTerm, setSearchTerm] = useState('')
  
  const students: Student[] = [
    {
      id: '1',
      name: '张三',
      phone: '13800138001',
      email: '<EMAIL>',
      enrollDate: '2024-01-15',
      course: '前端开发班',
      status: '在读',
      progress: 75
    },
    {
      id: '2',
      name: '李四',
      phone: '13800138002',
      email: '<EMAIL>',
      enrollDate: '2024-02-01',
      course: 'Java开发班',
      status: '在读',
      progress: 60
    },
    {
      id: '3',
      name: '王五',
      phone: '13800138003',
      email: '<EMAIL>',
      enrollDate: '2024-01-20',
      course: 'Python数据分析',
      status: '请假',
      progress: 45
    }
  ]

  const filteredStudents = students.filter(student =>
    student.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
    student.phone.includes(searchTerm) ||
    student.course.toLowerCase().includes(searchTerm.toLowerCase())
  )

  return (
    <div className="space-y-4">
      {/* 搜索和操作栏 */}
      <div className="flex flex-col sm:flex-row gap-4 justify-between items-start sm:items-center">
        <div className="flex-1 max-w-md">
          <input
            type="text"
            placeholder="搜索学员姓名、手机号或课程..."
            value={searchTerm}
            onChange={(e) => setSearchTerm((e.target as HTMLInputElement).value)}
            className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
          />
        </div>
        <button className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors">
          + 添加学员
        </button>
      </div>

      {/* 学员列表 */}
      <div className="grid gap-4">
        {filteredStudents.map((student) => (
          <Card key={student.id} className="hover:shadow-md transition-shadow">
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-4">
                  <div className="w-12 h-12 bg-blue-100 rounded-full flex items-center justify-center">
                    <span className="text-blue-600 font-semibold text-lg">
                      {student.name.charAt(0)}
                    </span>
                  </div>
                  <div>
                    <h3 className="font-semibold text-gray-900">{student.name}</h3>
                    <p className="text-sm text-gray-500">{student.phone}</p>
                    <p className="text-sm text-gray-500">{student.email}</p>
                  </div>
                </div>
                
                <div className="flex items-center space-x-6">
                  <div className="text-center">
                    <p className="text-sm text-gray-500">课程</p>
                    <p className="font-medium">{student.course}</p>
                  </div>
                  
                  <div className="text-center">
                    <p className="text-sm text-gray-500">入学日期</p>
                    <p className="font-medium">{student.enrollDate}</p>
                  </div>
                  
                  <div className="text-center">
                    <p className="text-sm text-gray-500">学习进度</p>
                    <div className="w-20 bg-gray-200 rounded-full h-2 mt-1">
                      <div 
                        className="bg-blue-600 h-2 rounded-full" 
                        style={{ width: `${student.progress}%` }}
                      ></div>
                    </div>
                    <p className="text-xs text-gray-600 mt-1">{student.progress}%</p>
                  </div>
                  
                  <div className="text-center">
                    <p className="text-sm text-gray-500">状态</p>
                    <span className={`inline-block px-2 py-1 rounded-full text-xs font-medium ${
                      student.status === '在读' ? 'bg-green-100 text-green-800' :
                      student.status === '请假' ? 'bg-yellow-100 text-yellow-800' :
                      'bg-gray-100 text-gray-800'
                    }`}>
                      {student.status}
                    </span>
                  </div>
                  
                  <div className="flex space-x-2">
                    <button className="p-2 text-gray-400 hover:text-blue-600 hover:bg-blue-50 rounded">
                      <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15.232 5.232l3.536 3.536m-2.036-5.036a2.5 2.5 0 113.536 3.536L6.5 21.036H3v-3.572L16.732 3.732z" />
                      </svg>
                    </button>
                    <button className="p-2 text-gray-400 hover:text-red-600 hover:bg-red-50 rounded">
                      <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
                      </svg>
                    </button>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>
    </div>
  )
}

function StudentPackages() {
  const packages: Package[] = [
    {
      id: '1',
      studentName: '张三',
      packageName: '前端开发全套课程',
      totalHours: 120,
      usedHours: 90,
      remainingHours: 30,
      startDate: '2024-01-15',
      endDate: '2024-06-15',
      status: '进行中',
      price: 12800
    },
    {
      id: '2',
      studentName: '李四',
      packageName: 'Java企业级开发',
      totalHours: 150,
      usedHours: 75,
      remainingHours: 75,
      startDate: '2024-02-01',
      endDate: '2024-08-01',
      status: '进行中',
      price: 15600
    }
  ]

  return (
    <div className="space-y-4">
      <div className="flex justify-between items-center">
        <h3 className="text-lg font-semibold">学员套餐管理</h3>
        <button className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors">
          + 添加套餐
        </button>
      </div>

      <div className="grid gap-4">
        {packages.map((pkg) => (
          <Card key={pkg.id}>
            <CardContent className="p-6">
              <div className="flex justify-between items-start">
                <div className="space-y-2">
                  <h4 className="font-semibold text-lg">{pkg.packageName}</h4>
                  <p className="text-gray-600">学员：{pkg.studentName}</p>
                  <div className="flex items-center space-x-4 text-sm text-gray-500">
                    <span>开始时间：{pkg.startDate}</span>
                    <span>结束时间：{pkg.endDate}</span>
                    <span>套餐价格：¥{pkg.price.toLocaleString()}</span>
                  </div>
                </div>
                
                <div className="text-right space-y-2">
                  <span className={`inline-block px-3 py-1 rounded-full text-sm font-medium ${
                    pkg.status === '进行中' ? 'bg-green-100 text-green-800' :
                    pkg.status === '已完成' ? 'bg-blue-100 text-blue-800' :
                    'bg-gray-100 text-gray-800'
                  }`}>
                    {pkg.status}
                  </span>
                  <div className="text-sm text-gray-600">
                    <p>总课时：{pkg.totalHours}小时</p>
                    <p>已用：{pkg.usedHours}小时</p>
                    <p>剩余：{pkg.remainingHours}小时</p>
                  </div>
                </div>
              </div>
              
              <div className="mt-4">
                <div className="flex justify-between text-sm text-gray-600 mb-1">
                  <span>课时使用进度</span>
                  <span>{Math.round((pkg.usedHours / pkg.totalHours) * 100)}%</span>
                </div>
                <div className="w-full bg-gray-200 rounded-full h-2">
                  <div 
                    className="bg-blue-600 h-2 rounded-full" 
                    style={{ width: `${(pkg.usedHours / pkg.totalHours) * 100}%` }}
                  ></div>
                </div>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>
    </div>
  )
}

function AdjustmentRecords() {
  const records: AdjustmentRecord[] = [
    {
      id: '1',
      studentName: '张三',
      productName: '前端开发课程',
      adjustmentType: '课时调整',
      oldValue: '100小时',
      newValue: '120小时',
      reason: '学员要求增加实战项目课时',
      operator: '管理员',
      date: '2024-03-15 14:30'
    },
    {
      id: '2',
      studentName: '李四',
      productName: 'Java开发课程',
      adjustmentType: '价格调整',
      oldValue: '¥15000',
      newValue: '¥13500',
      reason: '优惠活动折扣',
      operator: '张老师',
      date: '2024-03-10 09:20'
    }
  ]

  return (
    <div className="space-y-4">
      <h3 className="text-lg font-semibold">产品调整记录</h3>
      
      <div className="overflow-x-auto">
        <table className="w-full border-collapse">
          <thead>
            <tr className="border-b">
              <th className="text-left py-3 px-4 font-semibold text-gray-700">学员姓名</th>
              <th className="text-left py-3 px-4 font-semibold text-gray-700">产品名称</th>
              <th className="text-left py-3 px-4 font-semibold text-gray-700">调整类型</th>
              <th className="text-left py-3 px-4 font-semibold text-gray-700">原值</th>
              <th className="text-left py-3 px-4 font-semibold text-gray-700">新值</th>
              <th className="text-left py-3 px-4 font-semibold text-gray-700">调整原因</th>
              <th className="text-left py-3 px-4 font-semibold text-gray-700">操作人</th>
              <th className="text-left py-3 px-4 font-semibold text-gray-700">操作时间</th>
            </tr>
          </thead>
          <tbody>
            {records.map((record) => (
              <tr key={record.id} className="border-b hover:bg-gray-50">
                <td className="py-3 px-4">{record.studentName}</td>
                <td className="py-3 px-4">{record.productName}</td>
                <td className="py-3 px-4">
                  <span className="px-2 py-1 bg-blue-100 text-blue-800 rounded-full text-sm">
                    {record.adjustmentType}
                  </span>
                </td>
                <td className="py-3 px-4 text-gray-600">{record.oldValue}</td>
                <td className="py-3 px-4 font-medium">{record.newValue}</td>
                <td className="py-3 px-4 text-sm text-gray-600 max-w-xs truncate">{record.reason}</td>
                <td className="py-3 px-4">{record.operator}</td>
                <td className="py-3 px-4 text-sm text-gray-500">{record.date}</td>
              </tr>
            ))}
          </tbody>
        </table>
      </div>
    </div>
  )
}

function GraduatedStudents() {
  const graduatedStudents: GraduatedStudent[] = [
    {
      id: '1',
      name: '王小明',
      phone: '13800138004',
      graduationDate: '2023-12-15',
      course: '前端开发班',
      finalGrade: '优秀',
      certificateNumber: 'CERT-2023-001',
      currentJob: '某科技公司前端工程师'
    },
    {
      id: '2',
      name: '陈小红',
      phone: '13800138005',
      graduationDate: '2023-11-20',
      course: 'UI/UX设计班',
      finalGrade: '良好',
      certificateNumber: 'CERT-2023-002',
      currentJob: '设计公司UI设计师'
    }
  ]

  return (
    <div className="space-y-4">
      <div className="flex justify-between items-center">
        <h3 className="text-lg font-semibold">毕业学员</h3>
        <button className="px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors">
          导出毕业证书
        </button>
      </div>

      <div className="grid gap-4">
        {graduatedStudents.map((student) => (
          <Card key={student.id} className="hover:shadow-md transition-shadow">
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-4">
                  <div className="w-12 h-12 bg-green-100 rounded-full flex items-center justify-center">
                    <span className="text-green-600 font-semibold text-lg">
                      {student.name.charAt(0)}
                    </span>
                  </div>
                  <div>
                    <h4 className="font-semibold text-gray-900">{student.name}</h4>
                    <p className="text-sm text-gray-500">{student.phone}</p>
                    <p className="text-sm text-gray-500">证书编号：{student.certificateNumber}</p>
                  </div>
                </div>
                
                <div className="flex items-center space-x-6">
                  <div className="text-center">
                    <p className="text-sm text-gray-500">毕业课程</p>
                    <p className="font-medium">{student.course}</p>
                  </div>
                  
                  <div className="text-center">
                    <p className="text-sm text-gray-500">毕业日期</p>
                    <p className="font-medium">{student.graduationDate}</p>
                  </div>
                  
                  <div className="text-center">
                    <p className="text-sm text-gray-500">最终成绩</p>
                    <span className={`inline-block px-2 py-1 rounded-full text-sm font-medium ${
                      student.finalGrade === '优秀' ? 'bg-green-100 text-green-800' :
                      student.finalGrade === '良好' ? 'bg-blue-100 text-blue-800' :
                      'bg-yellow-100 text-yellow-800'
                    }`}>
                      {student.finalGrade}
                    </span>
                  </div>
                  
                  <div className="text-center">
                    <p className="text-sm text-gray-500">当前工作</p>
                    <p className="font-medium text-sm max-w-32 truncate">
                      {student.currentJob || '未录入'}
                    </p>
                  </div>
                  
                  <div className="flex space-x-2">
                    <button className="p-2 text-gray-400 hover:text-blue-600 hover:bg-blue-50 rounded" title="查看详情">
                      <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z" />
                      </svg>
                    </button>
                    <button className="p-2 text-gray-400 hover:text-green-600 hover:bg-green-50 rounded" title="下载证书">
                      <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                      </svg>
                    </button>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>
    </div>
  )
}

export function Students() {
  const [activeTab, setActiveTab] = useState<'current' | 'packages' | 'adjustments' | 'graduated'>('current')

  const tabs = [
    { key: 'current' as const, label: '在读学员', count: 3 },
    { key: 'packages' as const, label: '学员套餐', count: 2 },
    { key: 'adjustments' as const, label: '产品调整记录', count: 2 },
    { key: 'graduated' as const, label: '毕业学员', count: 2 }
  ]

  return (
    <div className="p-6">
      <div className="max-w-7xl mx-auto">
        {/* 页面标题 */}
        <div className="mb-6">
          <h1 className="text-2xl font-bold text-gray-900">学员管理</h1>
          <p className="text-gray-600 mt-1">管理所有学员信息、套餐和学习进度</p>
        </div>

        {/* 标签页导航 */}
        <div className="border-b border-gray-200 mb-6">
          <nav className="flex space-x-1">
            {tabs.map((tab) => (
              <TabButton
                key={tab.key}
                label={tab.label}
                active={activeTab === tab.key}
                onClick={() => setActiveTab(tab.key)}
                count={tab.count}
              />
            ))}
          </nav>
        </div>

        {/* 标签页内容 */}
        <div className="min-h-96">
          {activeTab === 'current' && <CurrentStudents />}
          {activeTab === 'packages' && <StudentPackages />}
          {activeTab === 'adjustments' && <AdjustmentRecords />}
          {activeTab === 'graduated' && <GraduatedStudents />}
        </div>
      </div>
    </div>
  )
}