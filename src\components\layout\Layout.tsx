import { useState } from 'preact/hooks'
import { Header } from './Header'
import { Sidebar } from './Sidebar'

interface LayoutProps {
  children: preact.ComponentChildren
}

export function Layout({ children }: LayoutProps) {
  const [sidebarOpen, setSidebarOpen] = useState(false)
  const [sidebarCollapsed, setSidebarCollapsed] = useState(false)

  const handleToggleSidebar = () => {
    setSidebarCollapsed(!sidebarCollapsed)
  }

  return (
    <div className="h-screen bg-gray-50 flex flex-col overflow-hidden">
      {/* Fixed header across full width */}
      <div className="flex-shrink-0 z-40">
        <Header 
          onMenuClick={() => setSidebarOpen(!sidebarOpen)}
          sidebarCollapsed={sidebarCollapsed}
          onToggleSidebar={handleToggleSidebar}
        />
      </div>

      {/* Content area with sidebar */}
      <div className="flex flex-1 overflow-hidden relative">
        {/* Desktop sidebar - Fixed */}
        <div className="hidden lg:block flex-shrink-0 z-30 relative">
          <Sidebar collapsed={sidebarCollapsed} />
        </div>

        {/* Mobile sidebar overlay */}
        {sidebarOpen && (
          <div className="lg:hidden">
            <div className="fixed inset-0 z-50 bg-black bg-opacity-25" onClick={() => setSidebarOpen(false)} />
            <div className="fixed inset-y-0 left-0 z-50 w-64" style={{ top: '48px' }}>
              <Sidebar />
            </div>
          </div>
        )}

        {/* Main content - Scrollable */}
        <main className="flex-1 overflow-y-auto bg-gray-50">
          {children}
        </main>
      </div>
    </div>
  )
}