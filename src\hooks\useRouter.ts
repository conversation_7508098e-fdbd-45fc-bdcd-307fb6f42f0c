import { useState, useEffect } from 'preact/hooks'

export interface RouteConfig {
  path: string
  component: () => JSX.Element
}

export function useRouter(routes: RouteConfig[]) {
  const [currentPath, setCurrentPath] = useState(() => {
    if (typeof window !== 'undefined') {
      return window.location.hash.slice(1) || '/'
    }
    return '/'
  })

  useEffect(() => {
    const handleHashChange = () => {
      setCurrentPath(window.location.hash.slice(1) || '/')
    }

    window.addEventListener('hashchange', handleHashChange)
    return () => window.removeEventListener('hashchange', handleHashChange)
  }, [])

  const navigate = (path: string) => {
    window.location.hash = path
    setCurrentPath(path)
  }

  const getCurrentComponent = () => {
    const route = routes.find(route => route.path === currentPath)
    return route?.component || routes.find(route => route.path === '/')?.component
  }

  return {
    currentPath,
    navigate,
    getCurrentComponent
  }
}