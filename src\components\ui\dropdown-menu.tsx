import { useState, useRef, useEffect, useContext } from 'preact/hooks'
import { createContext } from 'preact'
import { ComponentChildren } from 'preact'
import { cn } from '@/lib/utils'

interface DropdownMenuProps {
  children: ComponentChildren
}

interface DropdownMenuTriggerProps {
  children: ComponentChildren
  className?: string
}

interface DropdownMenuContentProps {
  children: ComponentChildren
  className?: string
}

interface DropdownMenuItemProps {
  children: ComponentChildren
  onClick?: () => void
  className?: string
}

interface DropdownMenuSeparatorProps {
  className?: string
}

interface DropdownContextType {
  isOpen: boolean
  setIsOpen: (open: boolean) => void
}

const DropdownContext = createContext<DropdownContextType | null>(null)

function DropdownMenu({ children }: DropdownMenuProps) {
  const [isOpen, setIsOpen] = useState(false)
  const dropdownRef = useRef<HTMLDivElement>(null)

  useEffect(() => {
    function handleClickOutside(event: MouseEvent) {
      if (dropdownRef.current && !dropdownRef.current.contains(event.target as Node)) {
        setIsOpen(false)
      }
    }

    if (isOpen) {
      document.addEventListener('mousedown', handleClickOutside)
    }
    
    return () => {
      document.removeEventListener('mousedown', handleClickOutside)
    }
  }, [isOpen])

  return (
    <DropdownContext.Provider value={{ isOpen, setIsOpen }}>
      <div ref={dropdownRef} className="relative inline-block text-left">
        {children}
      </div>
    </DropdownContext.Provider>
  )
}

function DropdownMenuTrigger({ children, className }: DropdownMenuTriggerProps) {
  const context = useContext(DropdownContext)
  
  if (!context) {
    throw new Error('DropdownMenuTrigger must be used within DropdownMenu')
  }

  const { isOpen, setIsOpen } = context

  const handleClick = (e: Event) => {
    e.preventDefault()
    e.stopPropagation()
    setIsOpen(!isOpen)
  }

  return (
    <button
      onClick={handleClick}
      type="button"
      className={cn('inline-flex items-center justify-center', className)}
    >
      {children}
    </button>
  )
}

function DropdownMenuContent({ children, className }: DropdownMenuContentProps) {
  const context = useContext(DropdownContext)
  
  if (!context) {
    throw new Error('DropdownMenuContent must be used within DropdownMenu')
  }

  const { isOpen } = context
  
  if (!isOpen) return null

  return (
    <div
      className={cn(
        'absolute right-0 z-50 mt-2 w-56 origin-top-right rounded-md bg-white shadow-lg ring-1 ring-black ring-opacity-5 focus:outline-none',
        className
      )}
    >
      <div className="py-1" role="menu">
        {children}
      </div>
    </div>
  )
}

function DropdownMenuItem({ children, onClick, className }: DropdownMenuItemProps) {
  const context = useContext(DropdownContext)
  
  if (!context) {
    throw new Error('DropdownMenuItem must be used within DropdownMenu')
  }

  const { setIsOpen } = context
  
  const handleClick = () => {
    onClick?.()
    setIsOpen(false)
  }

  return (
    <button
      onClick={handleClick}
      className={cn(
        'block w-full px-4 py-2 text-left text-sm text-gray-700 hover:bg-gray-100 hover:text-gray-900',
        className
      )}
      role="menuitem"
    >
      {children}
    </button>
  )
}

function DropdownMenuSeparator({ className }: DropdownMenuSeparatorProps) {
  return <div className={cn('my-1 h-px bg-gray-200', className)} />
}

export {
  DropdownMenu,
  DropdownMenuTrigger,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
}