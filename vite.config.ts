import { defineConfig } from 'vite'
import preact from '@preact/preset-vite'
import path from 'path'

// https://vite.dev/config/
export default defineConfig({
  plugins: [preact()],
  resolve: {
    alias: {
      '@': path.resolve(__dirname, './src'),
      'react': 'preact/compat',
      'react-dom': 'preact/compat'
    },
  },
  // server: {
  //   hmr: {
  //     overlay: true
  //   },
  //   watch: {
  //     usePolling: true,
  //     interval: 100
  //   }
  // },
  // optimizeDeps: {
  //   include: ['preact/hooks', 'preact/compat', '@reduxjs/toolkit', 'react-redux']
  // }
})
