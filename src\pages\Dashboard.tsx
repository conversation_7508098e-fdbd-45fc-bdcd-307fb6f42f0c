import { Layout } from '@/components/layout/Layout'
import { useRouter, RouteConfig } from '@/hooks/useRouter'
import { DashboardHome } from './DashboardHome'
import { Students } from './educational/Students'
import { InstitutionInfo } from './institution/InstitutionInfo'
import { AddressManagement } from './institution/AddressManagement'
import { StaffManagement } from './institution/StaffManagement'
import { TeacherManagement } from './institution/TeacherManagement'
import { OperationLogs } from './institution/OperationLogs'

export function Dashboard() {
  const routes: RouteConfig[] = [
    { path: '/', component: DashboardHome },
    { path: '/educational/students', component: Students },
    { path: '/institution/info', component: InstitutionInfo },
    { path: '/institution/address', component: AddressManagement },
    { path: '/institution/staff', component: StaffManagement },
    { path: '/institution/teachers', component: TeacherManagement },
    { path: '/institution/logs', component: OperationLogs }
  ]

  const { getCurrentComponent } = useRouter(routes)
  const CurrentComponent = getCurrentComponent()

  return (
    <Layout>
      {CurrentComponent ? <CurrentComponent /> : <DashboardHome />}
    </Layout>
  )
}