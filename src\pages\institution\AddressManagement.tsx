import { useState } from 'preact/hooks'
import { <PERSON>, <PERSON><PERSON><PERSON>nt, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'

interface Address {
  id: string
  name: string
  type: string
  province: string
  city: string
  district: string
  street: string
  detailAddress: string
  postalCode: string
  phone: string
  contactPerson: string
  isDefault: boolean
  isActive: boolean
  capacity?: number
  facilities?: string[]
  description?: string
  latitude?: number
  longitude?: number
}

function AddressForm({ 
  address, 
  onSave, 
  onCancel 
}: { 
  address?: Address | null
  onSave: (address: Omit<Address, 'id'>) => void
  onCancel: () => void 
}) {
  const [formData, setFormData] = useState<Omit<Address, 'id'>>({
    name: address?.name || '',
    type: address?.type || 'branch',
    province: address?.province || '',
    city: address?.city || '',
    district: address?.district || '',
    street: address?.street || '',
    detailAddress: address?.detailAddress || '',
    postalCode: address?.postalCode || '',
    phone: address?.phone || '',
    contactPerson: address?.contactPerson || '',
    isDefault: address?.isDefault || false,
    isActive: address?.isActive ?? true,
    capacity: address?.capacity || 0,
    facilities: address?.facilities || [],
    description: address?.description || ''
  })

  const handleSubmit = (e: Event) => {
    e.preventDefault()
    if (formData.name && formData.province && formData.city && formData.detailAddress) {
      onSave(formData)
    }
  }

  const handleFacilityChange = (facility: string, checked: boolean) => {
    if (checked) {
      setFormData({
        ...formData,
        facilities: [...(formData.facilities || []), facility]
      })
    } else {
      setFormData({
        ...formData,
        facilities: (formData.facilities || []).filter(f => f !== facility)
      })
    }
  }

  const availableFacilities = [
    '停车场', '电梯', '空调', '多媒体设备', '投影仪', '白板',
    '实验室', '图书馆', '咖啡厅', '休息区', '无线网络', '监控系统'
  ]

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <div className="bg-white rounded-lg max-w-4xl w-full max-h-[90vh] overflow-y-auto">
        <div className="p-6">
          <h2 className="text-xl font-bold text-gray-900 mb-6">
            {address ? '编辑地址' : '添加地址'}
          </h2>
          
          <form onSubmit={handleSubmit} className="space-y-6">
            {/* 基本信息 */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">地址名称 *</label>
                <input
                  type="text"
                  value={formData.name}
                  onChange={(e) => setFormData({
                    ...formData,
                    name: (e.target as HTMLInputElement).value
                  })}
                  placeholder="如：总部、分校区A"
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  required
                />
              </div>
              
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">地址类型</label>
                <select
                  value={formData.type}
                  onChange={(e) => setFormData({
                    ...formData,
                    type: (e.target as HTMLSelectElement).value
                  })}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                >
                  <option value="headquarters">总部</option>
                  <option value="branch">分校区</option>
                  <option value="office">办公室</option>
                  <option value="warehouse">仓库</option>
                  <option value="other">其他</option>
                </select>
              </div>
            </div>

            {/* 地址信息 */}
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">省份 *</label>
                <input
                  type="text"
                  value={formData.province}
                  onChange={(e) => setFormData({
                    ...formData,
                    province: (e.target as HTMLInputElement).value
                  })}
                  placeholder="如：上海市"
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  required
                />
              </div>
              
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">城市 *</label>
                <input
                  type="text"
                  value={formData.city}
                  onChange={(e) => setFormData({
                    ...formData,
                    city: (e.target as HTMLInputElement).value
                  })}
                  placeholder="如：上海市"
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  required
                />
              </div>
              
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">区县</label>
                <input
                  type="text"
                  value={formData.district}
                  onChange={(e) => setFormData({
                    ...formData,
                    district: (e.target as HTMLInputElement).value
                  })}
                  placeholder="如：浦东新区"
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                />
              </div>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">街道</label>
                <input
                  type="text"
                  value={formData.street}
                  onChange={(e) => setFormData({
                    ...formData,
                    street: (e.target as HTMLInputElement).value
                  })}
                  placeholder="如：张江高科技园区"
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                />
              </div>
              
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">邮政编码</label>
                <input
                  type="text"
                  value={formData.postalCode}
                  onChange={(e) => setFormData({
                    ...formData,
                    postalCode: (e.target as HTMLInputElement).value
                  })}
                  placeholder="如：200120"
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                />
              </div>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">详细地址 *</label>
              <input
                type="text"
                value={formData.detailAddress}
                onChange={(e) => setFormData({
                  ...formData,
                  detailAddress: (e.target as HTMLInputElement).value
                })}
                placeholder="如：科苑路399号张江创新大厦A座15楼"
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                required
              />
            </div>

            {/* 联系信息 */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">联系电话</label>
                <input
                  type="tel"
                  value={formData.phone}
                  onChange={(e) => setFormData({
                    ...formData,
                    phone: (e.target as HTMLInputElement).value
                  })}
                  placeholder="如：021-12345678"
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                />
              </div>
              
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">联系人</label>
                <input
                  type="text"
                  value={formData.contactPerson}
                  onChange={(e) => setFormData({
                    ...formData,
                    contactPerson: (e.target as HTMLInputElement).value
                  })}
                  placeholder="如：张经理"
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                />
              </div>
            </div>

            {/* 其他信息 */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">容纳人数</label>
                <input
                  type="number"
                  value={formData.capacity}
                  onChange={(e) => setFormData({
                    ...formData,
                    capacity: parseInt((e.target as HTMLInputElement).value) || 0
                  })}
                  placeholder="如：100"
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                />
              </div>
            </div>

            {/* 设施 */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">配套设施</label>
              <div className="grid grid-cols-2 md:grid-cols-4 gap-2">
                {availableFacilities.map((facility) => (
                  <label key={facility} className="flex items-center space-x-2">
                    <input
                      type="checkbox"
                      checked={formData.facilities?.includes(facility) || false}
                      onChange={(e) => handleFacilityChange(facility, (e.target as HTMLInputElement).checked)}
                      className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                    />
                    <span className="text-sm text-gray-700">{facility}</span>
                  </label>
                ))}
              </div>
            </div>

            {/* 描述 */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">地址描述</label>
              <textarea
                value={formData.description}
                onChange={(e) => setFormData({
                  ...formData,
                  description: (e.target as HTMLTextAreaElement).value
                })}
                rows={3}
                placeholder="补充说明或备注信息..."
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              />
            </div>

            {/* 状态设置 */}
            <div className="flex flex-wrap gap-4">
              <label className="flex items-center space-x-2">
                <input
                  type="checkbox"
                  checked={formData.isDefault}
                  onChange={(e) => setFormData({
                    ...formData,
                    isDefault: (e.target as HTMLInputElement).checked
                  })}
                  className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                />
                <span className="text-sm text-gray-700">设为默认地址</span>
              </label>
              
              <label className="flex items-center space-x-2">
                <input
                  type="checkbox"
                  checked={formData.isActive}
                  onChange={(e) => setFormData({
                    ...formData,
                    isActive: (e.target as HTMLInputElement).checked
                  })}
                  className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                />
                <span className="text-sm text-gray-700">启用此地址</span>
              </label>
            </div>

            {/* 按钮 */}
            <div className="flex justify-end space-x-4 pt-4 border-t">
              <Button
                type="button"
                variant="outline"
                onClick={onCancel}
              >
                取消
              </Button>
              <Button
                type="submit"
              >
                {address ? '更新' : '添加'}
              </Button>
            </div>
          </form>
        </div>
      </div>
    </div>
  )
}

function AddressList({ addresses, onEdit, onDelete }: {
  addresses: Address[]
  onEdit: (address: Address) => void
  onDelete: (id: string) => void
}) {
  const getTypeLabel = (type: string) => {
    const types = {
      headquarters: '总部',
      branch: '分校区',
      office: '办公室',
      warehouse: '仓库',
      other: '其他'
    }
    return types[type as keyof typeof types] || type
  }

  const getTypeColor = (type: string) => {
    const colors = {
      headquarters: 'bg-purple-100 text-purple-800',
      branch: 'bg-blue-100 text-blue-800',
      office: 'bg-green-100 text-green-800',
      warehouse: 'bg-orange-100 text-orange-800',
      other: 'bg-gray-100 text-gray-800'
    }
    return colors[type as keyof typeof colors] || 'bg-gray-100 text-gray-800'
  }

  return (
    <div className="grid gap-4">
      {addresses.map((address) => (
        <Card key={address.id} className="hover:shadow-md transition-shadow">
          <CardContent className="p-6">
            <div className="flex justify-between items-start mb-4">
              <div className="flex items-center space-x-3">
                <h3 className="text-lg font-semibold text-gray-900">{address.name}</h3>
                <span className={`px-2 py-1 rounded-full text-xs font-medium ${getTypeColor(address.type)}`}>
                  {getTypeLabel(address.type)}
                </span>
                {address.isDefault && (
                  <span className="px-2 py-1 bg-yellow-100 text-yellow-800 rounded-full text-xs font-medium">
                    默认
                  </span>
                )}
                <span className={`px-2 py-1 rounded-full text-xs font-medium ${
                  address.isActive 
                    ? 'bg-green-100 text-green-800' 
                    : 'bg-red-100 text-red-800'
                }`}>
                  {address.isActive ? '启用' : '禁用'}
                </span>
              </div>
              
              <div className="flex space-x-2">
                <Button
                  onClick={() => onEdit(address)}
                  variant="ghost"
                  size="sm"
                  className="p-2 h-8 w-8"
                >
                  <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15.232 5.232l3.536 3.536m-2.036-5.036a2.5 2.5 0 113.536 3.536L6.5 21.036H3v-3.572L16.732 3.732z" />
                  </svg>
                </Button>
                <Button
                  onClick={() => onDelete(address.id)}
                  variant="ghost"
                  size="sm"
                  className="p-2 h-8 w-8 text-red-600 hover:text-red-700 hover:bg-red-50"
                >
                  <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
                  </svg>
                </Button>
              </div>
            </div>
            
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 mb-4">
              <div>
                <p className="text-sm text-gray-500">完整地址</p>
                <p className="text-gray-900">
                  {address.province}{address.city}{address.district}
                  {address.street && `${address.street} `}
                  {address.detailAddress}
                </p>
              </div>
              
              {address.phone && (
                <div>
                  <p className="text-sm text-gray-500">联系电话</p>
                  <p className="text-gray-900">{address.phone}</p>
                </div>
              )}
              
              {address.contactPerson && (
                <div>
                  <p className="text-sm text-gray-500">联系人</p>
                  <p className="text-gray-900">{address.contactPerson}</p>
                </div>
              )}
              
              {address.capacity && address.capacity > 0 && (
                <div>
                  <p className="text-sm text-gray-500">容纳人数</p>
                  <p className="text-gray-900">{address.capacity}人</p>
                </div>
              )}
              
              {address.postalCode && (
                <div>
                  <p className="text-sm text-gray-500">邮政编码</p>
                  <p className="text-gray-900">{address.postalCode}</p>
                </div>
              )}
            </div>
            
            {address.facilities && address.facilities.length > 0 && (
              <div className="mb-4">
                <p className="text-sm text-gray-500 mb-2">配套设施</p>
                <div className="flex flex-wrap gap-2">
                  {address.facilities.map((facility, index) => (
                    <span key={index} className="px-2 py-1 bg-gray-100 text-gray-700 rounded text-sm">
                      {facility}
                    </span>
                  ))}
                </div>
              </div>
            )}
            
            {address.description && (
              <div>
                <p className="text-sm text-gray-500">描述</p>
                <p className="text-gray-700 text-sm">{address.description}</p>
              </div>
            )}
          </CardContent>
        </Card>
      ))}
    </div>
  )
}

export function AddressManagement() {
  const [addresses, setAddresses] = useState<Address[]>([
    {
      id: '1',
      name: '总部大楼',
      type: 'headquarters',
      province: '上海市',
      city: '上海市',
      district: '浦东新区',
      street: '张江高科技园区',
      detailAddress: '科苑路399号张江创新大厦A座15楼',
      postalCode: '200120',
      phone: '021-12345678',
      contactPerson: '张经理',
      isDefault: true,
      isActive: true,
      capacity: 200,
      facilities: ['停车场', '电梯', '空调', '多媒体设备', '无线网络', '监控系统'],
      description: '公司总部办公地点，包含行政部门和技术部门'
    },
    {
      id: '2',
      name: '浦西分校区',
      type: 'branch',
      province: '上海市',
      city: '上海市',
      district: '静安区',
      street: '南京西路',
      detailAddress: '南京西路1788号1788国际中心B座8楼',
      postalCode: '200040',
      phone: '021-87654321',
      contactPerson: '李老师',
      isDefault: false,
      isActive: true,
      capacity: 150,
      facilities: ['电梯', '空调', '投影仪', '白板', '休息区', '无线网络'],
      description: '浦西地区主要教学点，交通便利'
    }
  ])
  
  const [showForm, setShowForm] = useState(false)
  const [editingAddress, setEditingAddress] = useState<Address | null>(null)
  const [searchTerm, setSearchTerm] = useState('')
  const [filterType, setFilterType] = useState('all')

  const handleAddAddress = () => {
    setEditingAddress(null)
    setShowForm(true)
  }

  const handleEditAddress = (address: Address) => {
    setEditingAddress(address)
    setShowForm(true)
  }

  const handleSaveAddress = (addressData: Omit<Address, 'id'>) => {
    if (editingAddress) {
      // 更新地址
      setAddresses(addresses.map(addr => 
        addr.id === editingAddress.id 
          ? { ...addressData, id: editingAddress.id }
          : addr
      ))
    } else {
      // 添加新地址
      const newAddress: Address = {
        ...addressData,
        id: (addresses.length + 1).toString()
      }
      setAddresses([...addresses, newAddress])
    }
    setShowForm(false)
    setEditingAddress(null)
  }

  const handleDeleteAddress = (id: string) => {
    if (confirm('确定要删除这个地址吗？')) {
      setAddresses(addresses.filter(addr => addr.id !== id))
    }
  }

  const filteredAddresses = addresses.filter(address => {
    const matchesSearch = address.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         address.detailAddress.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         address.contactPerson?.toLowerCase().includes(searchTerm.toLowerCase())
    
    const matchesType = filterType === 'all' || address.type === filterType
    
    return matchesSearch && matchesType
  })

  return (
    <div className="p-6">
      <div className="max-w-7xl mx-auto">
        {/* 页面标题和操作 */}
        <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center mb-6">
          <div>
            <h1 className="text-2xl font-bold text-gray-900">地址管理</h1>
            <p className="text-gray-600 mt-1">管理机构的所有地址信息</p>
          </div>
          <Button
            onClick={handleAddAddress}
            className="mt-4 sm:mt-0"
          >
            + 添加地址
          </Button>
        </div>

        {/* 搜索和筛选 */}
        <Card className="mb-6">
          <CardContent className="p-4">
            <div className="flex flex-col sm:flex-row gap-4">
              <div className="flex-1">
                <input
                  type="text"
                  placeholder="搜索地址名称、详细地址或联系人..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm((e.target as HTMLInputElement).value)}
                  className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                />
              </div>
              <div className="sm:w-48">
                <select
                  value={filterType}
                  onChange={(e) => setFilterType((e.target as HTMLSelectElement).value)}
                  className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                >
                  <option value="all">所有类型</option>
                  <option value="headquarters">总部</option>
                  <option value="branch">分校区</option>
                  <option value="office">办公室</option>
                  <option value="warehouse">仓库</option>
                  <option value="other">其他</option>
                </select>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* 地址列表 */}
        {filteredAddresses.length > 0 ? (
          <AddressList 
            addresses={filteredAddresses}
            onEdit={handleEditAddress}
            onDelete={handleDeleteAddress}
          />
        ) : (
          <Card>
            <CardContent className="p-12 text-center">
              <svg className="w-16 h-16 text-gray-400 mx-auto mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z" />
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 11a3 3 0 11-6 0 3 3 0 016 0z" />
              </svg>
              <h3 className="text-lg font-medium text-gray-900 mb-2">暂无地址信息</h3>
              <p className="text-gray-500 mb-4">您还没有添加任何地址，点击上方按钮添加第一个地址</p>
              <Button
                onClick={handleAddAddress}
              >
                添加地址
              </Button>
            </CardContent>
          </Card>
        )}

        {/* 地址表单弹窗 */}
        {showForm && (
          <AddressForm
            address={editingAddress}
            onSave={handleSaveAddress}
            onCancel={() => {
              setShowForm(false)
              setEditingAddress(null)
            }}
          />
        )}
      </div>
    </div>
  )
}