# 机构中心功能完善说明

## 概述
本次更新完善了机构中心模块的相关页面，使用 Tailwind CSS 和 shadcn/ui 组件库实现了现代化的用户界面。

## 新增页面

### 1. 员工管理 (`/institution/staff`)
**文件位置**: `src/pages/institution/StaffManagement.tsx`

**功能特性**:
- ✅ 员工列表展示（卡片式布局）
- ✅ 添加/编辑员工信息
- ✅ 员工信息包含：
  - 基本信息（姓名、邮箱、手机号、职位）
  - 部门分类（行政部、教务部、财务部等）
  - 员工状态（在职、离职、待入职）
  - 入职日期、薪资
  - 地址信息
  - 紧急联系人
  - 权限设置
- ✅ 搜索和筛选功能（按部门、状态筛选）
- ✅ 删除员工功能
- ✅ 响应式设计

### 2. 教师管理 (`/institution/teachers`)
**文件位置**: `src/pages/institution/TeacherManagement.tsx`

**功能特性**:
- ✅ 教师列表展示（卡片式布局）
- ✅ 添加/编辑教师信息
- ✅ 教师信息包含：
  - 基本信息（姓名、邮箱、手机号）
  - 专业领域（数学、英语、编程等）
  - 教学经验年限
  - 学历信息
  - 教师状态
  - 课时费设置
  - 个人简介
  - 评分和统计信息（学员数、课程数）
- ✅ 搜索和筛选功能（按专业、状态筛选）
- ✅ 删除教师功能
- ✅ 响应式设计

### 3. 操作日志 (`/institution/logs`)
**文件位置**: `src/pages/institution/OperationLogs.tsx`

**功能特性**:
- ✅ 操作日志列表展示
- ✅ 日志信息包含：
  - 操作者信息
  - 操作类型（创建、更新、删除、查看、登录等）
  - 操作模块
  - 操作目标
  - 操作详情
  - 时间戳
  - IP地址
  - 操作状态（成功、失败、警告）
  - 操作耗时
- ✅ 多维度筛选功能：
  - 按模块筛选
  - 按操作类型筛选
  - 按状态筛选
  - 按日期范围筛选
- ✅ 搜索功能
- ✅ 重置筛选功能

## 优化的现有页面

### 1. 机构信息页面优化
**文件位置**: `src/pages/institution/InstitutionInfo.tsx`

**优化内容**:
- ✅ 使用 shadcn/ui Button 组件替换原生按钮
- ✅ 统一按钮样式和交互效果
- ✅ 改进视觉一致性

### 2. 地址管理页面优化
**文件位置**: `src/pages/institution/AddressManagement.tsx`

**优化内容**:
- ✅ 使用 shadcn/ui Button 组件替换原生按钮
- ✅ 统一按钮样式和交互效果
- ✅ 改进视觉一致性

## 路由配置更新

**文件位置**: `src/pages/Dashboard.tsx`

新增路由配置：
```typescript
{ path: '/institution/staff', component: StaffManagement },
{ path: '/institution/teachers', component: TeacherManagement },
{ path: '/institution/logs', component: OperationLogs }
```

## 技术栈

- **框架**: Preact
- **样式**: Tailwind CSS
- **组件库**: shadcn/ui
- **状态管理**: Redux Toolkit
- **类型检查**: TypeScript

## 设计特点

### 1. 一致的视觉设计
- 统一的卡片布局
- 一致的颜色方案
- 标准化的按钮和表单元素

### 2. 响应式设计
- 移动端友好
- 自适应网格布局
- 灵活的搜索和筛选栏

### 3. 用户体验优化
- 直观的操作流程
- 清晰的状态指示
- 友好的空状态提示
- 确认对话框防止误操作

### 4. 功能完整性
- 完整的CRUD操作
- 多维度搜索筛选
- 数据验证
- 状态管理

## 数据结构

### 员工数据结构
```typescript
interface Staff {
  id: string
  name: string
  email: string
  phone: string
  position: string
  department: string
  status: 'active' | 'inactive' | 'pending'
  joinDate: string
  permissions: string[]
  salary?: number
  address?: string
  emergencyContact?: {
    name: string
    phone: string
    relationship: string
  }
}
```

### 教师数据结构
```typescript
interface Teacher {
  id: string
  name: string
  email: string
  phone: string
  specialties: string[]
  experience: number
  education: string
  status: 'active' | 'inactive' | 'pending'
  joinDate: string
  rating: number
  totalStudents: number
  totalCourses: number
  hourlyRate: number
  bio?: string
  certifications: string[]
  languages: string[]
}
```

### 操作日志数据结构
```typescript
interface OperationLog {
  id: string
  operator: string
  operatorId: string
  action: string
  module: string
  target: string
  targetId?: string
  details: string
  timestamp: string
  ip: string
  userAgent: string
  status: 'success' | 'failed' | 'warning'
  duration?: number
}
```

## 使用说明

1. 启动项目：`npm run dev`
2. 访问 `http://localhost:5173`
3. 在侧边栏中找到"机构中心"模块
4. 点击相应的子菜单项访问各个功能页面

## 后续扩展建议

1. **数据持久化**: 集成后端API接口
2. **权限控制**: 基于用户角色的功能访问控制
3. **数据导出**: 支持Excel/PDF格式导出
4. **批量操作**: 支持批量编辑和删除
5. **高级筛选**: 更多筛选条件和保存筛选配置
6. **通知系统**: 操作成功/失败的消息提示
7. **数据统计**: 添加图表和统计分析功能
