import { Provider } from 'react-redux'
import { store } from '@/store'
import { AuthProvider } from '@/components/auth/AuthProvider'
import { useAppSelector } from '@/hooks/redux'
import { Login } from '@/pages/Login'
import { Dashboard } from '@/pages/Dashboard'

function AppContent() {
  const { isAuthenticated } = useAppSelector((state) => state.auth)

  if (!isAuthenticated) {
    return <Login />
  }

  return <Dashboard />
}

export function App() {
  return (
    <Provider store={store}>
      <AuthProvider>
        <AppContent />
      </AuthProvider>
    </Provider>
  )
}
