import { useState } from 'preact/hooks'
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON>it<PERSON> } from '@/components/ui/card'
import { Button } from '@/components/ui/button'

interface OperationLog {
  id: string
  operator: string
  operatorId: string
  action: string
  module: string
  target: string
  targetId?: string
  details: string
  timestamp: string
  ip: string
  userAgent: string
  status: 'success' | 'failed' | 'warning'
  duration?: number
}

function LogList({ logs }: { logs: OperationLog[] }) {
  const getStatusColor = (status: OperationLog['status']) => {
    const colors = {
      success: 'bg-green-100 text-green-800',
      failed: 'bg-red-100 text-red-800',
      warning: 'bg-yellow-100 text-yellow-800'
    }
    return colors[status]
  }

  const getStatusLabel = (status: OperationLog['status']) => {
    const labels = {
      success: '成功',
      failed: '失败',
      warning: '警告'
    }
    return labels[status]
  }

  const getActionIcon = (action: string) => {
    const icons: Record<string, any> = {
      create: (
        <svg className="w-4 h-4 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
        </svg>
      ),
      update: (
        <svg className="w-4 h-4 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15.232 5.232l3.536 3.536m-2.036-5.036a2.5 2.5 0 113.536 3.536L6.5 21.036H3v-3.572L16.732 3.732z" />
        </svg>
      ),
      delete: (
        <svg className="w-4 h-4 text-red-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
        </svg>
      ),
      login: (
        <svg className="w-4 h-4 text-purple-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M11 16l-4-4m0 0l4-4m-4 4h14m-5 4v1a3 3 0 01-3 3H6a3 3 0 01-3-3V7a3 3 0 013-3h7a3 3 0 013 3v1" />
        </svg>
      ),
      logout: (
        <svg className="w-4 h-4 text-gray-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17 16l4-4m0 0l-4-4m4 4H7m6 4v1a3 3 0 01-3 3H6a3 3 0 01-3-3V7a3 3 0 013-3h4a3 3 0 013 3v1" />
        </svg>
      ),
      view: (
        <svg className="w-4 h-4 text-indigo-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z" />
        </svg>
      )
    }
    return icons[action] || icons.view
  }

  const formatTimestamp = (timestamp: string) => {
    const date = new Date(timestamp)
    return date.toLocaleString('zh-CN', {
      year: 'numeric',
      month: '2-digit',
      day: '2-digit',
      hour: '2-digit',
      minute: '2-digit',
      second: '2-digit'
    })
  }

  return (
    <div className="space-y-4">
      {logs.map((log) => (
        <Card key={log.id} className="hover:shadow-sm transition-shadow">
          <CardContent className="p-4">
            <div className="flex items-start justify-between">
              <div className="flex items-start space-x-3 flex-1">
                <div className="flex-shrink-0 mt-1">
                  {getActionIcon(log.action)}
                </div>
                <div className="flex-1 min-w-0">
                  <div className="flex items-center space-x-2 mb-1">
                    <span className="font-medium text-gray-900">{log.operator}</span>
                    <span className="text-gray-500">在</span>
                    <span className="text-blue-600 font-medium">{log.module}</span>
                    <span className="text-gray-500">模块</span>
                    <span className={`px-2 py-1 rounded-full text-xs font-medium ${getStatusColor(log.status)}`}>
                      {getStatusLabel(log.status)}
                    </span>
                  </div>
                  <p className="text-sm text-gray-600 mb-2">{log.details}</p>
                  <div className="flex items-center space-x-4 text-xs text-gray-500">
                    <span>目标: {log.target}</span>
                    <span>IP: {log.ip}</span>
                    <span>时间: {formatTimestamp(log.timestamp)}</span>
                    {log.duration && <span>耗时: {log.duration}ms</span>}
                  </div>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>
      ))}
    </div>
  )
}

export function OperationLogs() {
  const [logs, setLogs] = useState<OperationLog[]>([
    {
      id: '1',
      operator: '张三',
      operatorId: 'user_001',
      action: 'create',
      module: '学员管理',
      target: '学员信息',
      targetId: 'student_123',
      details: '创建了新学员"李小明"的档案信息',
      timestamp: '2024-01-15T10:30:00Z',
      ip: '***********00',
      userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
      status: 'success',
      duration: 245
    },
    {
      id: '2',
      operator: '李四',
      operatorId: 'user_002',
      action: 'update',
      module: '课程管理',
      target: '课程信息',
      targetId: 'course_456',
      details: '修改了"高中数学基础班"的课程价格从800元调整为900元',
      timestamp: '2024-01-15T09:15:00Z',
      ip: '*************',
      userAgent: 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36',
      status: 'success',
      duration: 156
    },
    {
      id: '3',
      operator: '王五',
      operatorId: 'user_003',
      action: 'delete',
      module: '员工管理',
      target: '员工信息',
      targetId: 'staff_789',
      details: '删除了离职员工"赵六"的账户信息',
      timestamp: '2024-01-15T08:45:00Z',
      ip: '***********02',
      userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
      status: 'warning',
      duration: 89
    },
    {
      id: '4',
      operator: '管理员',
      operatorId: 'admin_001',
      action: 'login',
      module: '系统管理',
      target: '用户登录',
      details: '管理员账户登录系统',
      timestamp: '2024-01-15T08:00:00Z',
      ip: '***********',
      userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
      status: 'success',
      duration: 1200
    },
    {
      id: '5',
      operator: '张三',
      operatorId: 'user_001',
      action: 'update',
      module: '机构信息',
      target: '联系方式',
      targetId: 'contact_001',
      details: '更新了机构客服电话号码',
      timestamp: '2024-01-14T16:20:00Z',
      ip: '***********00',
      userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
      status: 'success',
      duration: 78
    },
    {
      id: '6',
      operator: '李四',
      operatorId: 'user_002',
      action: 'view',
      module: '财务管理',
      target: '销售报表',
      details: '查看了2024年1月份的销售数据报表',
      timestamp: '2024-01-14T15:30:00Z',
      ip: '*************',
      userAgent: 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36',
      status: 'success',
      duration: 234
    }
  ])

  const [searchTerm, setSearchTerm] = useState('')
  const [filterModule, setFilterModule] = useState('all')
  const [filterAction, setFilterAction] = useState('all')
  const [filterStatus, setFilterStatus] = useState('all')
  const [dateRange, setDateRange] = useState({
    start: '',
    end: ''
  })

  const modules = Array.from(new Set(logs.map(log => log.module)))
  const actions = Array.from(new Set(logs.map(log => log.action)))

  const filteredLogs = logs.filter(log => {
    const matchesSearch = log.operator.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         log.details.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         log.target.toLowerCase().includes(searchTerm.toLowerCase())
    
    const matchesModule = filterModule === 'all' || log.module === filterModule
    const matchesAction = filterAction === 'all' || log.action === filterAction
    const matchesStatus = filterStatus === 'all' || log.status === filterStatus
    
    let matchesDate = true
    if (dateRange.start && dateRange.end) {
      const logDate = new Date(log.timestamp)
      const startDate = new Date(dateRange.start)
      const endDate = new Date(dateRange.end)
      matchesDate = logDate >= startDate && logDate <= endDate
    }
    
    return matchesSearch && matchesModule && matchesAction && matchesStatus && matchesDate
  })

  return (
    <div className="p-6">
      <div className="max-w-7xl mx-auto">
        {/* 页面标题 */}
        <div className="mb-6">
          <h1 className="text-2xl font-bold text-gray-900">操作日志</h1>
          <p className="text-gray-600 mt-1">查看系统的所有操作记录和审计信息</p>
        </div>

        {/* 搜索和筛选 */}
        <Card className="mb-6">
          <CardContent className="p-4">
            <div className="space-y-4">
              <div className="flex flex-col sm:flex-row gap-4">
                <div className="flex-1">
                  <input
                    type="text"
                    placeholder="搜索操作者、操作详情或目标..."
                    value={searchTerm}
                    onChange={(e) => setSearchTerm((e.target as HTMLInputElement).value)}
                    className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  />
                </div>
                <div className="flex gap-2">
                  <select
                    value={filterModule}
                    onChange={(e) => setFilterModule((e.target as HTMLSelectElement).value)}
                    className="px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  >
                    <option value="all">所有模块</option>
                    {modules.map(module => (
                      <option key={module} value={module}>{module}</option>
                    ))}
                  </select>
                  <select
                    value={filterAction}
                    onChange={(e) => setFilterAction((e.target as HTMLSelectElement).value)}
                    className="px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  >
                    <option value="all">所有操作</option>
                    <option value="create">创建</option>
                    <option value="update">更新</option>
                    <option value="delete">删除</option>
                    <option value="view">查看</option>
                    <option value="login">登录</option>
                    <option value="logout">登出</option>
                  </select>
                  <select
                    value={filterStatus}
                    onChange={(e) => setFilterStatus((e.target as HTMLSelectElement).value)}
                    className="px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  >
                    <option value="all">所有状态</option>
                    <option value="success">成功</option>
                    <option value="failed">失败</option>
                    <option value="warning">警告</option>
                  </select>
                </div>
              </div>
              
              <div className="flex gap-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">开始日期</label>
                  <input
                    type="date"
                    value={dateRange.start}
                    onChange={(e) => setDateRange({
                      ...dateRange,
                      start: (e.target as HTMLInputElement).value
                    })}
                    className="px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">结束日期</label>
                  <input
                    type="date"
                    value={dateRange.end}
                    onChange={(e) => setDateRange({
                      ...dateRange,
                      end: (e.target as HTMLInputElement).value
                    })}
                    className="px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  />
                </div>
                <div className="flex items-end">
                  <Button
                    variant="outline"
                    onClick={() => {
                      setSearchTerm('')
                      setFilterModule('all')
                      setFilterAction('all')
                      setFilterStatus('all')
                      setDateRange({ start: '', end: '' })
                    }}
                  >
                    重置筛选
                  </Button>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* 日志列表 */}
        {filteredLogs.length > 0 ? (
          <LogList logs={filteredLogs} />
        ) : (
          <Card>
            <CardContent className="p-12 text-center">
              <div className="w-16 h-16 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-4">
                <svg className="w-8 h-8 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                </svg>
              </div>
              <h3 className="text-lg font-medium text-gray-900 mb-2">暂无日志记录</h3>
              <p className="text-gray-500">没有找到符合条件的操作日志</p>
            </CardContent>
          </Card>
        )}
      </div>
    </div>
  )
}
